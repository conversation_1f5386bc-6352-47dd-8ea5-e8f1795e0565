"use client"

import Image from 'next/image'
import Link from 'next/link'
import {
  SignInButton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
} from '@clerk/nextjs'
import Button from '../ui/Button'
import { strs } from '@/constants/components/navigation'
import { usePathname } from 'next/navigation'

export default function Navigation() {
  const pathname = usePathname()
  const isHomePage = pathname === '/' || pathname === '/home'
  
  return (
    <header className={isHomePage 
      ? "!fixed top-6 left-1/2 -translate-x-1/2 z-50 navigation-background rounded w-[75%] h-[3.5rem] flex justify-between items-center pl-8 pr-[0.75rem] gap-4" 
      : "fixed z-50 w-full h-page-padding flex justify-between items-center p-6 gap-4"
    }>
      <Link href="/home">
        <Image src="/brand/logo.svg" alt={strs.logoAlt} width={100} height={100} className="h-5 w-auto cursor-pointer" />
      </Link>
      <div className="flex items-center gap-2">
        <SignedOut>
          <SignInButton>
            <Button styleClassName="white-button" className="px-6" >
              {strs.logIn}
            </Button>
          </SignInButton>
          <SignUpButton>
            <Button styleClassName="blue-button" className="px-6" >
              {strs.signUp}
            </Button>
          </SignUpButton>
        </SignedOut>
        <SignedIn>
          <UserButton 
            appearance={{
              elements: {
                userButtonPopoverFooter: {
                  display: "none"
                }
              }
            }}
          />
        </SignedIn>
      </div>
    </header>
  )
}