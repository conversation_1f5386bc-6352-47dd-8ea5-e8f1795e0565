"use client";

import { memo } from "react";
import type { BrowseItem } from "@/data/browseContentData";
import BrowseItemCard from "./BrowseItemCard";

interface BrowseSectionProps {
    title: string;
    items: BrowseItem[];
    onItemClick?: (item: BrowseItem) => void;
}

const BrowseSection = memo(({ title, items, onItemClick }: BrowseSectionProps) => {
    return (
        <div className="flex flex-col gap-4">
            <h2 className="text-sm leading-[100%] capitalize">{title.replace(/-/g, ' ')}</h2>
            <div className="grid grid-cols-1 gap-4">
                {items.map((item) => (
                    <BrowseItemCard
                        key={item.id}
                        item={item}
                        onClick={onItemClick}
                    />
                ))}
            </div>
        </div>
    );
});

BrowseSection.displayName = "BrowseSection";

export default BrowseSection;