<svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_622_1159)">
<ellipse cx="4.27601" cy="3.67691" rx="2.1113" ry="2.13323" transform="rotate(25.4025 4.27601 3.67691)" fill="url(#paint0_linear_622_1159)"/>
</g>
<g filter="url(#filter1_f_622_1159)">
<circle cx="4.3913" cy="4.04644" r="1.84865" transform="rotate(25.4025 4.3913 4.04644)" fill="url(#paint1_linear_622_1159)"/>
</g>
<g filter="url(#filter2_f_622_1159)">
<circle cx="4.39126" cy="4.04667" r="1.84865" transform="rotate(-169.598 4.39126 4.04667)" fill="url(#paint2_linear_622_1159)"/>
</g>
<g filter="url(#filter3_f_622_1159)">
<circle cx="4.45034" cy="4.10526" r="1.84865" transform="rotate(-169.598 4.45034 4.10526)" fill="url(#paint3_linear_622_1159)"/>
</g>
<defs>
<filter id="filter0_f_622_1159" x="1.65486" y="1.04206" width="5.24229" height="5.26987" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0" result="effect1_foregroundBlur_622_1159"/>
</filter>
<filter id="filter1_f_622_1159" x="0.890772" y="0.545801" width="7.00117" height="7.00117" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0" result="effect1_foregroundBlur_622_1159"/>
</filter>
<filter id="filter2_f_622_1159" x="1.55629" y="1.21181" width="5.6699" height="5.66965" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.0369731" result="effect1_foregroundBlur_622_1159"/>
</filter>
<filter id="filter3_f_622_1159" x="1.0238" y="0.678829" width="6.85303" height="6.85279" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.788759" result="effect1_foregroundBlur_622_1159"/>
</filter>
<linearGradient id="paint0_linear_622_1159" x1="4.37424" y1="4.0417" x2="5.95075" y2="5.74318" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F3DA6"/>
<stop offset="0.951923" stop-color="#3A9AFF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_622_1159" x1="3.12758" y1="3.96653" x2="4.63306" y2="5.84385" gradientUnits="userSpaceOnUse">
<stop offset="0.00961538" stop-color="#0D50E8"/>
<stop offset="0.951923" stop-color="#3A9AFF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_622_1159" x1="6.27638" y1="5.45292" x2="3.34899" y2="1.79267" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F3DA6" stop-opacity="0"/>
<stop offset="1" stop-color="#59ABFF"/>
</linearGradient>
<linearGradient id="paint3_linear_622_1159" x1="6.33547" y1="5.51151" x2="5.11244" y2="2.60346" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F3DA6" stop-opacity="0"/>
<stop offset="1" stop-color="#59ABFF"/>
</linearGradient>
</defs>
</svg>
