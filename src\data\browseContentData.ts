export interface BrowseItem {
    id: string;
    title: string;
    preview: string;
    video: string;
    category: string;
    subCategory: string;
}

export const browseContentData: Record<string, Record<string, BrowseItem[]>> = {
    sections: {
        hero: [
            { id: "hero-1", title: "Modern Hero", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "sections", subCategory: "hero" },
            { id: "hero-2", title: "Minimal Hero", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "sections", subCategory: "hero" },
            { id: "hero-3", title: "Video Hero", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "sections", subCategory: "hero" },
            { id: "hero-4", title: "Split Hero", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "sections", subCategory: "hero" },
        ],
        features: [
            { id: "feat-1", title: "3 Column Features", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "sections", subCategory: "features" },
            { id: "feat-2", title: "Icon Grid", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "sections", subCategory: "features" },
            { id: "feat-3", title: "Feature Cards", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "sections", subCategory: "features" },
        ],
        pricing: [
            { id: "price-1", title: "Pricing Table", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "sections", subCategory: "pricing" },
            { id: "price-2", title: "Pricing Cards", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "sections", subCategory: "pricing" },
            { id: "price-3", title: "Compare Plans", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "sections", subCategory: "pricing" },
        ],
        testimonials: [
            { id: "test-1", title: "Customer Reviews", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "sections", subCategory: "testimonials" },
            { id: "test-2", title: "Logo Cloud", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "sections", subCategory: "testimonials" },
            { id: "test-3", title: "Case Studies", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "sections", subCategory: "testimonials" },
        ],
        contact: [
            { id: "contact-1", title: "Contact Form", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "sections", subCategory: "contact" },
            { id: "contact-2", title: "Contact Info", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "sections", subCategory: "contact" },
        ],
        footer: [
            { id: "footer-1", title: "Simple Footer", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "sections", subCategory: "footer" },
            { id: "footer-2", title: "Mega Footer", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "sections", subCategory: "footer" },
            { id: "footer-3", title: "Newsletter Footer", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "sections", subCategory: "footer" },
        ],
        about: [
            { id: "about-1", title: "Company Story", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "sections", subCategory: "about" },
            { id: "about-2", title: "Mission Vision", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "sections", subCategory: "about" },
            { id: "about-3", title: "Timeline About", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "sections", subCategory: "about" },
        ],
        team: [
            { id: "team-1", title: "Team Grid", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "sections", subCategory: "team" },
            { id: "team-2", title: "Team Carousel", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "sections", subCategory: "team" },
        ],
        faq: [
            { id: "faq-1", title: "Accordion FAQ", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "sections", subCategory: "faq" },
            { id: "faq-2", title: "Grid FAQ", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "sections", subCategory: "faq" },
        ],
        cta: [
            { id: "cta-1", title: "Centered CTA", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "sections", subCategory: "cta" },
            { id: "cta-2", title: "Split CTA", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "sections", subCategory: "cta" },
            { id: "cta-3", title: "Background CTA", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "sections", subCategory: "cta" },
        ],
        stats: [
            { id: "stats-1", title: "Counter Stats", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "sections", subCategory: "stats" },
            { id: "stats-2", title: "Progress Stats", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "sections", subCategory: "stats" },
        ],
        portfolio: [
            { id: "portfolio-1", title: "Portfolio Grid", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "sections", subCategory: "portfolio" },
            { id: "portfolio-2", title: "Portfolio Masonry", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "sections", subCategory: "portfolio" },
            { id: "portfolio-3", title: "Portfolio Filter", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "sections", subCategory: "portfolio" },
        ],
    },
    components: {
        buttons: [
            { id: "btn-1", title: "Primary Button", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "components", subCategory: "buttons" },
            { id: "btn-2", title: "Ghost Button", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "components", subCategory: "buttons" },
            { id: "btn-3", title: "Icon Button", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "components", subCategory: "buttons" },
            { id: "btn-4", title: "Loading Button", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "components", subCategory: "buttons" },
        ],
        cards: [
            { id: "card-1", title: "Product Card", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "components", subCategory: "cards" },
            { id: "card-2", title: "Blog Card", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "components", subCategory: "cards" },
            { id: "card-3", title: "Profile Card", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "components", subCategory: "cards" },
        ],
        forms: [
            { id: "form-1", title: "Login Form", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "components", subCategory: "forms" },
            { id: "form-2", title: "Signup Form", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "components", subCategory: "forms" },
            { id: "form-3", title: "Multi-step Form", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "components", subCategory: "forms" },
        ],
        navigation: [
            { id: "nav-1", title: "Top Navigation", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "components", subCategory: "navigation" },
            { id: "nav-2", title: "Side Navigation", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "components", subCategory: "navigation" },
            { id: "nav-3", title: "Mobile Menu", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "components", subCategory: "navigation" },
        ],
        modals: [
            { id: "modal-1", title: "Dialog Modal", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "components", subCategory: "modals" },
            { id: "modal-2", title: "Fullscreen Modal", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "components", subCategory: "modals" },
        ],
        badges: [
            { id: "badge-1", title: "Status Badge", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "components", subCategory: "badges" },
            { id: "badge-2", title: "Notification Badge", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "components", subCategory: "badges" },
        ],
        tabs: [
            { id: "tabs-1", title: "Simple Tabs", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "components", subCategory: "tabs" },
            { id: "tabs-2", title: "Icon Tabs", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "components", subCategory: "tabs" },
            { id: "tabs-3", title: "Vertical Tabs", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "components", subCategory: "tabs" },
        ],
        accordions: [
            { id: "acc-1", title: "Basic Accordion", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "components", subCategory: "accordions" },
            { id: "acc-2", title: "Nested Accordion", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "components", subCategory: "accordions" },
        ],
        alerts: [
            { id: "alert-1", title: "Info Alert", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "components", subCategory: "alerts" },
            { id: "alert-2", title: "Success Alert", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "components", subCategory: "alerts" },
            { id: "alert-3", title: "Warning Alert", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "components", subCategory: "alerts" },
        ],
        tooltips: [
            { id: "tooltip-1", title: "Basic Tooltip", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "components", subCategory: "tooltips" },
            { id: "tooltip-2", title: "Rich Tooltip", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "components", subCategory: "tooltips" },
        ],
        progress: [
            { id: "progress-1", title: "Linear Progress", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "components", subCategory: "progress" },
            { id: "progress-2", title: "Circular Progress", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "components", subCategory: "progress" },
        ],
        breadcrumbs: [
            { id: "bread-1", title: "Simple Breadcrumb", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "components", subCategory: "breadcrumbs" },
            { id: "bread-2", title: "Icon Breadcrumb", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "components", subCategory: "breadcrumbs" },
        ],
    },
    text: {
        headings: [
            { id: "head-1", title: "Display Heading", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "text", subCategory: "headings" },
            { id: "head-2", title: "Section Heading", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "text", subCategory: "headings" },
            { id: "head-3", title: "Gradient Heading", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "text", subCategory: "headings" },
        ],
        paragraphs: [
            { id: "para-1", title: "Lead Paragraph", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "text", subCategory: "paragraphs" },
            { id: "para-2", title: "Body Text", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "text", subCategory: "paragraphs" },
        ],
        quotes: [
            { id: "quote-1", title: "Blockquote", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "text", subCategory: "quotes" },
            { id: "quote-2", title: "Pull Quote", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "text", subCategory: "quotes" },
        ],
        lists: [
            { id: "list-1", title: "Bullet List", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "text", subCategory: "lists" },
            { id: "list-2", title: "Numbered List", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "text", subCategory: "lists" },
        ],
        typography: [
            { id: "typo-1", title: "Font Pairing", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "text", subCategory: "typography" },
            { id: "typo-2", title: "Type Scale", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "text", subCategory: "typography" },
        ],
        labels: [
            { id: "label-1", title: "Simple Label", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "text", subCategory: "labels" },
            { id: "label-2", title: "Badge Label", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "text", subCategory: "labels" },
        ],
        captions: [
            { id: "caption-1", title: "Image Caption", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "text", subCategory: "captions" },
            { id: "caption-2", title: "Figure Caption", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "text", subCategory: "captions" },
        ],
        highlights: [
            { id: "highlight-1", title: "Marker Highlight", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "text", subCategory: "highlights" },
            { id: "highlight-2", title: "Gradient Highlight", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "text", subCategory: "highlights" },
            { id: "highlight-3", title: "Underline Highlight", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "text", subCategory: "highlights" },
        ],
        dividers: [
            { id: "divider-1", title: "Line Divider", preview: "/images/vaultimage5.avif", video: "https://osmo.b-cdn.net/resource-preview/momentum-based-hover-1440x900.mp4", category: "text", subCategory: "dividers" },
            { id: "divider-2", title: "Icon Divider", preview: "/images/vaultimage.avif", video: "https://osmo.b-cdn.net/resource-preview/locomotive-smooth-scroll-1440x900-v2.mp4", category: "text", subCategory: "dividers" },
        ],
        code: [
            { id: "code-1", title: "Inline Code", preview: "/images/vaultimage2.avif", video: "https://osmo.b-cdn.net/resource-preview/follow-svg-on-scroll-1440x900.mp4", category: "text", subCategory: "code" },
            { id: "code-2", title: "Code Block", preview: "/images/vaultimage3.avif", video: "https://osmo.b-cdn.net/resource-preview/flick-cards-slider-1440x900.mp4", category: "text", subCategory: "code" },
            { id: "code-3", title: "Syntax Highlight", preview: "/images/vaultimage4.avif", video: "https://osmo.b-cdn.net/resource-preview/multilayer-navigation-1440x900.mp4", category: "text", subCategory: "code" },
        ],
    },
};