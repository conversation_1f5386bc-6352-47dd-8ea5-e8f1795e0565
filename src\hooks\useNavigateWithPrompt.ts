import { useRouter } from "next/navigation";
import { useCallback } from "react";

export const useNavigateWithPrompt = () => {
    const router = useRouter();

    const navigateWithPrompt = useCallback((path: string, prompt?: string) => {
        if (prompt?.trim()) {
            const params = new URLSearchParams({ prompt: prompt.trim() });
            router.push(`${path}?${params.toString()}`);
        } else {
            router.push(path);
        }
    }, [router]);

    return navigateWithPrompt;
};