@import "tailwindcss";

:root {
  /* Base units */
  --vw: clamp(0.5rem, 0.8vw, 1rem);

  /* colors */
  --background: #f5f8ff;
  --foreground: #171717;
  --color-transparent-grey: #f8f9f8;
  --color-off-white: rgba(245, 245, 245, 0.8);
  --color-blue: #5A71E6;

  /* text sizing */
  --text-2xs: clamp(0.575rem, 0.598vw, 0.805rem);
  --text-xs: clamp(0.6325rem, 0.736vw, 0.828rem);
  --text-sm: clamp(0.69rem, 0.828vw, 0.92rem);
  --text-base: clamp(0.805rem, 0.92vw, 1.035rem);
  --text-lg: clamp(0.92rem, 1.058vw, 1.15rem);
  --text-xl: clamp(0.98rem, 1.225vw, 1.31rem);
  --text-2xl: clamp(1.15rem, 1.518vw, 1.61rem);
  --text-3xl: clamp(1.38rem, 1.748vw, 1.84rem);
  --text-4xl: clamp(1.61rem, 2.3vw, 2.07rem);
  --text-5xl: clamp(2.66rem, 4.25vw, 3.4rem);

  /* Base spacing units */
  --vw-0_25: calc(var(--vw) * 0.25);
  --vw-0_5: calc(var(--vw) * 0.5);
  --vw-0_625: calc(var(--vw) * 0.625);
  --vw-0_75: calc(var(--vw) * 0.75);
  --vw-1: var(--vw);
  --vw-1_25: calc(var(--vw) * 1.25);
  --vw-1_5: calc(var(--vw) * 1.5);
  --vw-1_75: calc(var(--vw) * 1.75);
  --vw-2: calc(var(--vw) * 2);
  --vw-2_25: calc(var(--vw) * 2.25);
  --vw-2_5: calc(var(--vw) * 2.5);
  --vw-2_75: calc(var(--vw) * 2.75);
  --vw-3: calc(var(--vw) * 3);

  --vh-0_25: calc(var(--vh) * 0.25);
  --vh-0_5: calc(var(--vh) * 0.5);
  --vh-0_75: calc(var(--vh) * 0.75);
  --vh-1: var(--vh);
  --vh-1_25: calc(var(--vh) * 1.25);
  --vh-1_5: calc(var(--vh) * 1.5);
  --vh-1_75: calc(var(--vh) * 1.75);
  --vh-2: calc(var(--vh) * 2);
  --vh-2_25: calc(var(--vh) * 2.25);
  --vh-2_5: calc(var(--vh) * 2.5);
  --vh-3: calc(var(--vh) * 3);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-transparent-grey: var(--color-transparent-grey);
  --color-off-white: var(--color-off-white);
  --color-blue: var(--color-blue);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* text */
  --text-2xs: var(--text-2xs);
  --text-xs: var(--text-xs);
  --text-sm: var(--text-sm);
  --text-base: var(--text-base);
  --text-lg: var(--text-lg);
  --text-xl: var(--text-xl);
  --text-2xl: var(--text-2xl);
  --text-3xl: var(--text-3xl);
  --text-4xl: var(--text-4xl);
  --text-5xl: var(--text-5xl);

  /* height */
  --height-4: 1rem;
  --height-5: 1.25rem;
  --height-6: 1.5rem;
  --height-7: 1.75rem;
  --height-8: 2rem;
  --height-9: 2.25rem;
  --height-10: 2.5rem;
  --height-11: 2.75rem;
  --height-12: 3rem;

  --height-page-padding: calc(2.25rem+var(--vw-1_5)+var(--vw-1_5));

  /* gap */
  --spacing-1: var(--vw-0_25);
  --spacing-2: var(--vw-0_5);
  --spacing-3: var(--vw-0_75);
  --spacing-4: var(--vw-1);
  --spacing-5: var(--vw-1_25);
  --spacing-6: var(--vw-1_5);
  --spacing-7: var(--vw-1_75);
  --spacing-8: var(--vw-2);

  --spacing-x-1: var(--vw-0_25);
  --spacing-x-2: var(--vw-0_5);
  --spacing-x-3: var(--vw-0_75);
  --spacing-x-4: var(--vw-1);
  --spacing-x-5: var(--vw-1_25);
  --spacing-x-6: var(--vw-1_5);

  /* border radius */
  --radius-none: 0;
  --radius-sm: var(--vw-1);
  --radius: var(--vw-1_5);
  --radius-md: var(--vw-1_75);
  --radius-lg: var(--vw-2);
  --radius-xl: var(--vh-1_5);
  --radius-full: 9999px;

  /* padding */
  --padding-1: var(--vw-0_25);
  --padding-2: var(--vw-0_5);
  --padding-2.5: var(--vw-0_625);
  --padding-3: var(--vw-0_75);
  --padding-4: var(--vw-1);
  --padding-5: var(--vw-1_25);
  --padding-6: var(--vw-1_5);
  --padding-7: var(--vw-1_75);
  --padding-8: var(--vw-2);

  --padding-x-1: var(--vw-0_25);
  --padding-x-2: var(--vw-0_5);
  --padding-x-3: var(--vw-0_75);
  --padding-x-4: var(--vw-1);
  --padding-x-5: var(--vw-1_25);
  --padding-x-6: var(--vw-1_5);
  --padding-x-7: var(--vw-1_75);
  --padding-x-8: var(--vw-2);

  --padding-page-padding: calc(2.25rem+var(--vw-1_5)+var(--vw-1_5));

  /* margin */
  --margin-1: var(--vw-0_25);
  --margin-2: var(--vw-0_5);
  --margin-3: var(--vw-0_75);
  --margin-4: var(--vw-1);
  --margin-5: var(--vw-1_25);
  --margin-6: var(--vw-1_5);
  --margin-7: var(--vw-1_75);
  --margin-8: var(--vw-2);

  --margin-x-1: var(--vw-0_25);
  --margin-x-2: var(--vw-0_5);
  --margin-x-3: var(--vw-0_75);
  --margin-x-4: var(--vw-1);
  --margin-x-5: var(--vw-1_25);
  --margin-x-6: var(--vw-1_5);
  --margin-x-7: var(--vw-1_75);
  --margin-x-8: var(--vw-2);

}

@layer components {

  /* UI */

  .button {
    @apply relative cursor-pointer h-10 rounded-full px-4;
  }

}

@layer utilities {

  /* UI */

  .black-button {
    @apply text-white;
    background: linear-gradient(180deg, #000000 0%, #2C2C2C 74%);
    box-shadow: 0 10px 18px -7px rgba(0, 0, 0, 0.15);
  }

  .black-button::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-full p-[2px];
    background: linear-gradient(0deg, #585858 0%, #373737 27%, #000000 62%, #000000 100%);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .white-button {
    @apply text-black;
    background: linear-gradient(180deg, #EDF0FD 0%, #EFEFF2 74%);
    box-shadow: 0 10px 18px -7px rgba(0, 0, 0, 0.15);
  }

  .white-button-shadow {
    box-shadow: 0 10px 18px -7px rgba(0, 0, 0, 0.3);
  }

  .white-button::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-full p-[2px];
    background: linear-gradient(0deg, #E9EDFF 0%, #B5BBDF 27%, #E0E6F5 62%, #FFFFFF 100%);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .white-button-thin::before {
    @apply absolute pointer-events-none inset-0 rounded-full p-[1px];
  }

  .white-button-rounded::before {
    @apply rounded;
  }

  .white-button-rounded-sm::before {
    @apply rounded-sm;
  }

  .blue-button {
    @apply text-white;
    background: linear-gradient(180deg, #C7D0FB 0%, #7080CE 74%);
    box-shadow: 0 10px 18px -7px rgba(112, 128, 206, 1);
  }

  .blue-button::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-full p-[2px];
    background: linear-gradient(0deg, #C5CFFF 0%, rgba(93, 107, 179, 0.44) 18%, rgba(93, 107, 179, 0.66) 46%, #D3DAFF 100%);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .white-border {
    @apply relative rounded-md;
  }

  .white-border::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-md p-[1px];
    background: linear-gradient(0deg, #FFFFFF 0%, #FFFFFF 14%, #FFFFFF 100%);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .white-box {
    @apply relative backdrop-blur-sm;
    box-shadow:
      0 4px 18.6px rgba(68, 74, 80, 0.08),
      inset 0 0 16.3px rgba(255, 255, 255, 1);
    background-color: rgba(255, 255, 255, 0.45);
  }

  .white-box::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded p-[1px] opacity-35;
    background: linear-gradient(140deg, #B4B4B4 0%, #FFFFFF 14%, #CCCCCC 100%);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .white-circle {
    position: relative;
    background: linear-gradient(0deg, #F2F2F4 30%, rgba(189, 200, 255, 0.25) 60%, rgba(189, 200, 255, 0.5) 90%, rgba(189, 200, 255, 0.8) 100%);
    box-shadow: 0 2px 0 rgba(255, 255, 255, 0.75);
  }

  .white-circle::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded p-[1px] opacity-35;
    background: linear-gradient(90deg, #D4DBFF 0%, rgba(212, 219, 255, 0.43) 52%);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .glass-box {
    @apply relative rounded-md backdrop-blur-xs;
    background-color: rgba(255, 255, 255, 0.12);
  }

  .glass-box::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-md p-[1px];
    background: linear-gradient(0deg, #FFFFFF 0%, #FFFFFF 14%, #FFFFFF 100%);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .button-circle {
    @apply h-7 aspect-square rounded-full flex items-center justify-center;
  }

  .big-box-gradient {
    background: linear-gradient(180deg, white 0%, #EEEFF5 100%);
  }

  .green-box {
    @apply relative rounded-full;
    background: linear-gradient(180deg, #1AD9B3 0%, #4BAE9A 100%);
  }

  .green-box::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-full p-[1px];
    background: linear-gradient(0deg, #D4FFEE 0%, rgba(212, 255, 245, 0.43) 100%);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .white-button-variation {
    @apply text-black;
    background: linear-gradient(180deg, #FFFFFF 0%, #ffffff 74%);
    box-shadow: 0 10px 18px -7px rgba(0, 0, 0, 0.15);
  }

  .white-button-variation::before {
    content: "";
    @apply absolute pointer-events-none inset-0 rounded-full p-[2px];
    background: linear-gradient(0deg, #FAFAFA 0%, #E4E4E4 21%, #EAEAEA 34%, #FFFFFF 100%);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .navigation-background {
    backdrop-filter: blur(6px);
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 9999px;
    box-shadow: rgba(30, 45, 82, 0.06) 0px 0px 0.34px 0.34px, rgba(30, 45, 82, 0.2) 0.34px 0.34px 0.34px 0px;
  }

  /* TEXT */

  /* .gradient-text {
    width: fit-content;
    background-image: radial-gradient(47.08% 208.33% at 79.71% 128.33%, rgba(193, 221, 253, 0.8) 11.69%, rgba(90, 113, 230, .8) 35.44%, #000000 70.24%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  } */

  .gradient-text {
    width: fit-content;
    background-image: radial-gradient(47.08% 208.33% at 77.5% 128.33%, rgba(135, 206, 250, 0.8) 20.69%, rgba(90, 113, 230, .8) 35.44%, #000000 70.24%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  /* MASKS */

  .mask-fade-y {
    mask-image: linear-gradient(to bottom, transparent 0%, black var(--vw-1_5), black calc(100% - var(--vw-1_5)), transparent 100%);
  }

  .mask-fade-bottom {
    mask-image: linear-gradient(to bottom, black 0%, black calc(100% - var(--vw-1_5)), transparent 100%);
  }

  .mask-fade-bottom-border {
    mask-image: linear-gradient(to bottom, transparent 0%, black 1rem, black calc(100% - 1rem - 1px), transparent calc(100% - 1px), black calc(100% - 1px), black 100%);
  }

  .mask-fade-edges-2px {
    mask-image:
      linear-gradient(to right, transparent 0px, black 2px, black calc(100% - 2px), transparent 100%),
      linear-gradient(to bottom, transparent 0px, black 2px, black calc(100% - 2px), transparent 100%);
    mask-composite: intersect;
  }

  /* ANIMATION */

  @keyframes fadeInOut {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0;
    }
  }

  .logo-glow-animation {
    animation: fadeInOut 2s cubic-bezier(0.37, 0, 0.63, 1) infinite;
  }

  @keyframes moveUpDown {

    0%,
    100% {
      transform: translateY(1px);
    }

    50% {
      transform: translateY(-2px);
    }
  }

  .logo-dot-animation {
    animation: moveUpDown 2s cubic-bezier(0.37, 0, 0.63, 1) infinite;
  }

  .animation-container {
    animation:
      fadeInOpacity 0.8s ease-in-out forwards,
      fadeInTranslate 0.6s forwards;
  }

  .animation-container-fade {
    animation: fadeInOpacity 0.8s ease-in-out forwards;
  }

  @keyframes circleAnimation {

    0%,
    100% {
      transform: scale(1);
    }

    50% {
      transform: scale(0.65);
    }
  }

  @keyframes colorWave {

    0%,
    100% {
      color: black;
    }

    50% {
      color: #5A71E6;
    }
  }

  .continuous-color-animation {
    animation: colorWave 2s ease-in-out infinite;
  }

  @keyframes fadeInOpacity {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes fadeInTranslate {
    from {
      transform: translateY(0.75vh);
    }

    to {
      transform: translateY(0vh);
    }
  }

  @keyframes scan {

    0%,
    100% {
      transform: translateY(-5rem);
    }

    50% {
      transform: translateY(5rem);
    }
  }

}

@media (prefers-color-scheme: dark) {
  :root {}
}

* {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 1) rgba(255, 255, 255, 0);
}


body {
  background: var(--background);
  color: var(--foreground);
  font-family: "Inter Tight", sans-serif;
  position: relative;
  min-height: 100vh;
}

/* body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/background.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
} */

/* CLERK */

.cl-internal-zar2af {
  @apply shadow-none bg-white;
}

/* clerk button */

.cl-internal-1o76iuo {
  @apply !border-none !shadow-none rounded-full h-10 transition-none;
  background: linear-gradient(180deg, #BEC8F6 0%, #7887D1 74%);
}

.cl-internal-1o76iuo:hover {
  @apply shadow-none transform-none opacity-100;
  background: linear-gradient(180deg, #BEC8F6 0%, #7887D1 74%) !important;
}

.cl-internal-1o76iuo:hover::before,
.cl-internal-1o76iuo:hover::after,
.cl-internal-1o76iuo:hover * {
  @apply transition-none transform-none opacity-100;
}

.cl-internal-1o76iuo,
.cl-internal-1o76iuo * {
  @apply transition-none;
}

.cl-internal-1o76iuo:active,
.cl-internal-1o76iuo:focus {
  @apply transform-none;
  background: linear-gradient(180deg, #BEC8F6 0%, #7887D1 74%) !important;
}

.cl-internal-1o76iuo::before {
  content: "";
  @apply absolute pointer-events-none inset-0 rounded-full p-[2px];
  background: linear-gradient(0deg, #BFC9FB 0%, #838FCE 27%, #5A6390 62%, #C9D2FA 100%);
  mask:
    linear-gradient(#000 0 0) content-box,
    linear-gradient(#000 0 0);
  mask-composite: exclude;
}

.cl-internal-1uq3j8z {
  @apply hidden;
}