import { memo } from "react";

interface UserMessageProps {
    content: string;
}

const UserMessage = memo(({ content }: UserMessageProps) => {
    return (
        <div className="relative flex items-center rounded white-button white-button-rounded white-button-thin px-4 py-3 min-h-8 h-fit">
            <p className="text-sm leading-relaxed whitespace-pre-wrap text-black">
                {content}
            </p>
        </div>
    );
});

UserMessage.displayName = "UserMessage";

export default UserMessage;