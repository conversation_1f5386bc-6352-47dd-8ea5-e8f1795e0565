"use client";

import type { BrowseSubItem } from "@/types/browse";
import { getSubItemButtonClass } from "@/utils/browse/sidebar";

interface SubItemButtonProps {
    subItem: BrowseSubItem;
    isActive: boolean;
    onClick: () => void;
}

const SubItemButton = ({ subItem, isActive, onClick }: SubItemButtonProps) => {
    return (
        <button
            onClick={onClick}
            className={getSubItemButtonClass(isActive)}
        >
            <span className="text-xs text-black whitespace-nowrap flex-grow text-left">
                {subItem.label}
            </span>
            {subItem.count !== undefined && (
                <div className="flex justify-center items-center">
                    <span className="text-xs text-black">
                        {subItem.count}
                    </span>
                </div>
            )}
        </button>
    );
};

export default SubItemButton;