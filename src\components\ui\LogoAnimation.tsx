import { memo } from "react";
import { LogoAnimationProps } from "@/types/components/LogoAnimation";

export default memo(function LogoAnimation({
  className = "",
  width = 119,
  height = 36
}: LogoAnimationProps) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 119 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* main text shape */}
      <path d="M46.9561 12.3333C50.0633 12.3334 52.6019 13.4313 54.3613 15.3918C56.1184 17.35 57.0829 20.15 57.083 23.5276V25.2737H41.5156V25.3508C41.5156 27.229 42.0801 28.7702 43.0693 29.8401C44.0572 30.9083 45.4855 31.5247 47.25 31.5247C49.7609 31.5244 51.5779 30.2633 52.1543 28.4348L52.1973 28.2981H56.8643L56.8291 28.5237C56.1819 32.6656 52.2632 35.5821 47.1445 35.5823C43.8617 35.5822 41.2082 34.47 39.377 32.4592C37.5477 30.4505 36.5576 27.5656 36.5576 24.052C36.5577 20.5587 37.5633 17.6283 39.374 15.5686C41.1871 13.5066 43.7936 12.3333 46.9561 12.3333ZM64.1211 16.1135C65.5321 13.7738 67.9913 12.3755 70.9863 12.3752C73.8183 12.3752 76.1697 13.4995 77.8086 15.5188C79.4441 17.5345 80.3584 20.4272 80.3584 23.9475C80.3583 27.4776 79.4499 30.3744 77.8145 32.3928C76.1753 34.4155 73.8186 35.5402 70.9658 35.5403C67.881 35.5401 65.4411 34.1306 63.9961 31.803V35.1633H59.2051V4.53931H64.1211V16.1135ZM119 35.1633H114.21V31.8118C112.812 34.1007 110.361 35.5402 107.303 35.5403C104.46 35.5402 102.088 34.4051 100.431 32.3782C98.777 30.355 97.8468 27.4579 97.8467 23.9475C97.8467 20.4368 98.7772 17.5445 100.426 15.5266C102.077 13.505 104.439 12.3753 107.261 12.3752C110.267 12.3753 112.712 13.785 114.062 15.9924V4.53931H119V35.1633ZM70.665 35.3401C70.7646 35.3432 70.8648 35.345 70.9658 35.345L70.666 35.3401C70.5209 35.3356 70.3773 35.3276 70.2354 35.3167L70.665 35.3401ZM5.32715 4.6897L10.8203 27.9426L17.0996 4.68384L17.1387 4.53931H21.7959L21.835 4.68384L28.1338 27.9446L33.6289 4.6897L33.6641 4.53931H38.9561L38.8896 4.7854L30.7188 35.0188L30.6797 35.1633H25.9619L25.9209 35.0217L19.4668 12.5168L13.0352 35.0217L12.9941 35.1633H8.25488L8.21582 35.0188L0.0664062 4.7854L0 4.53931H5.29102L5.32715 4.6897ZM95.7441 35.1633H90.8281V4.53931H95.7441V35.1633ZM87.0918 35.1077H82.1758V12.676H87.0918V35.1077ZM49.8105 35.1018C49.8206 35.0996 49.8308 35.0982 49.8408 35.0959C49.8431 35.0954 49.8453 35.0945 49.8477 35.094C49.8353 35.0968 49.8229 35.0991 49.8105 35.1018ZM50.2305 35.0002C50.2402 34.9977 50.2501 34.996 50.2598 34.9934C50.2768 34.9889 50.2936 34.9834 50.3105 34.9788C50.2839 34.986 50.2572 34.9932 50.2305 35.0002ZM82.3711 34.9124H86.8965V34.9114H82.3711V34.9124ZM51.0352 34.7542C51.0458 34.7505 51.0568 34.7481 51.0674 34.7444C51.0862 34.7378 51.1043 34.7296 51.123 34.7229C51.0938 34.7334 51.0646 34.7439 51.0352 34.7542ZM51.4062 34.6174C51.4228 34.611 51.4396 34.6054 51.4561 34.5989C51.4676 34.5943 51.4787 34.5889 51.4902 34.5842C51.4623 34.5954 51.4343 34.6064 51.4062 34.6174ZM52.165 34.2825C52.1765 34.2768 52.1887 34.2725 52.2002 34.2668C52.2104 34.2618 52.2203 34.2563 52.2305 34.2512C52.2089 34.262 52.1868 34.2718 52.165 34.2825ZM52.5322 34.093C52.5399 34.0888 52.548 34.0855 52.5557 34.0813C52.5745 34.071 52.5926 34.0595 52.6113 34.0491C52.585 34.0638 52.5587 34.0785 52.5322 34.093ZM69.75 16.5793C68.0952 16.5796 66.6896 17.3036 65.6934 18.5803C64.6943 19.8609 64.1006 21.7073 64.1006 23.9475C64.1007 26.2086 64.695 28.0535 65.6934 29.3284C66.6891 30.5995 68.0942 31.3144 69.75 31.3147C71.4719 31.3147 72.8546 30.6192 73.8115 29.3625C74.7729 28.1 75.3163 26.2555 75.3164 23.9475C75.3164 21.6609 74.7733 19.8152 73.8115 18.5471C72.8541 17.285 71.4708 16.5793 69.75 16.5793ZM108.497 16.5793C106.765 16.5794 105.367 17.2856 104.396 18.5481C103.422 19.8163 102.868 21.6615 102.868 23.9475C102.868 26.2438 103.421 28.0884 104.396 29.3538C105.366 30.6136 106.765 31.3146 108.497 31.3147C110.175 31.3146 111.568 30.6051 112.548 29.3381C113.531 28.0665 114.104 26.2211 114.104 23.9475C114.104 21.6951 113.531 19.8488 112.548 18.5715C111.568 17.299 110.174 16.5794 108.497 16.5793ZM10.8984 28.4094H10.7305V28.4104H10.8984L17.2891 4.73462H17.2881L10.8984 28.4094ZM28.0576 28.4104H28.2256L28.2246 28.4094H28.0576V28.4104ZM102.698 24.7991C102.712 25.0178 102.732 25.2319 102.756 25.4417C102.731 25.2235 102.711 25.0005 102.697 24.7727L102.698 24.7991ZM114.294 23.5667C114.298 23.6923 114.3 23.8193 114.3 23.9475L114.294 24.3694C114.298 24.2304 114.301 24.0897 114.301 23.9475C114.301 23.8193 114.298 23.6924 114.294 23.5667ZM75.5068 23.5774C75.5102 23.6995 75.5117 23.8229 75.5117 23.9475L75.5059 24.3665C75.5099 24.2284 75.5127 24.0888 75.5127 23.9475C75.5127 23.698 75.5055 23.4535 75.4922 23.2141L75.5068 23.5774ZM52.3613 21.9153L52.3457 21.6018C52.3443 21.5819 52.3424 21.5621 52.3408 21.5422C52.3504 21.6651 52.3574 21.7895 52.3613 21.9153ZM46.9561 16.3694C43.9799 16.3695 41.8558 18.5812 41.5547 21.719H52.1562C52.0683 20.1605 51.5314 18.8458 50.6631 17.9124C49.7591 16.9407 48.486 16.3695 46.9561 16.3694ZM52.167 20.4133C52.1931 20.5236 52.2153 20.6358 52.2363 20.7493C52.2152 20.6355 52.1931 20.523 52.167 20.4124V20.4133ZM65.2168 18.9153C65.2095 18.9266 65.2026 18.9381 65.1953 18.9495C65.2462 18.8701 65.2981 18.7922 65.3516 18.7161L65.2168 18.9153ZM104.831 17.7776C104.772 17.8328 104.715 17.8893 104.658 17.9475C104.715 17.8893 104.772 17.8328 104.831 17.7776ZM105.179 17.4778C105.144 17.5052 105.109 17.5334 105.075 17.5618C105.109 17.5334 105.144 17.5052 105.179 17.4778ZM105.563 17.2024C105.447 17.2774 105.334 17.358 105.224 17.4426C105.332 17.3592 105.444 17.2803 105.559 17.2063C105.56 17.2052 105.562 17.2035 105.563 17.2024ZM105.794 17.0637C105.716 17.1081 105.639 17.1539 105.563 17.2024C105.639 17.1539 105.716 17.1081 105.794 17.0637ZM106.053 16.927C105.98 16.9628 105.908 17.0001 105.838 17.0393C105.908 17.0001 105.98 16.9628 106.053 16.927ZM107.203 16.5237C106.811 16.6115 106.44 16.7399 106.093 16.9075C106.266 16.824 106.445 16.7498 106.63 16.6858C106.815 16.6215 107.007 16.5679 107.203 16.5237ZM107.841 16.4182C107.746 16.4283 107.652 16.4409 107.56 16.4553C107.652 16.4409 107.746 16.4282 107.841 16.4182Z" fill="black" />
      {/* main text mask */}
      <mask id="mask0_1088_92" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="0" y="4" width="119" height="32">
        <path d="M46.9561 12.3333C50.0633 12.3334 52.6019 13.4313 54.3613 15.3918C56.1184 17.35 57.0829 20.15 57.083 23.5276V25.2737H41.5156V25.3508C41.5156 27.229 42.0801 28.7702 43.0693 29.8401C44.0572 30.9083 45.4855 31.5247 47.25 31.5247C49.7609 31.5244 51.5779 30.2633 52.1543 28.4348L52.1973 28.2981H56.8643L56.8291 28.5237C56.1819 32.6656 52.2632 35.5821 47.1445 35.5823C43.8617 35.5822 41.2082 34.47 39.377 32.4592C37.5477 30.4505 36.5576 27.5656 36.5576 24.052C36.5577 20.5587 37.5633 17.6283 39.374 15.5686C41.1871 13.5066 43.7936 12.3333 46.9561 12.3333ZM64.1211 16.1135C65.5321 13.7738 67.9913 12.3755 70.9863 12.3752C73.8183 12.3752 76.1697 13.4995 77.8086 15.5188C79.4441 17.5345 80.3584 20.4272 80.3584 23.9475C80.3583 27.4776 79.4499 30.3744 77.8145 32.3928C76.1753 34.4155 73.8186 35.5402 70.9658 35.5403C67.881 35.5401 65.4411 34.1306 63.9961 31.803V35.1633H59.2051V4.53931H64.1211V16.1135ZM119 35.1633H114.21V31.8118C112.812 34.1007 110.361 35.5402 107.303 35.5403C104.46 35.5402 102.088 34.4051 100.431 32.3782C98.777 30.355 97.8468 27.4579 97.8467 23.9475C97.8467 20.4368 98.7772 17.5445 100.426 15.5266C102.077 13.505 104.439 12.3753 107.261 12.3752C110.267 12.3753 112.712 13.785 114.062 15.9924V4.53931H119V35.1633ZM70.665 35.3401C70.7646 35.3432 70.8648 35.345 70.9658 35.345L70.666 35.3401C70.5209 35.3356 70.3773 35.3276 70.2354 35.3167L70.665 35.3401ZM5.32715 4.6897L10.8203 27.9426L17.0996 4.68384L17.1387 4.53931H21.7959L21.835 4.68384L28.1338 27.9446L33.6289 4.6897L33.6641 4.53931H38.9561L38.8896 4.7854L30.7188 35.0188L30.6797 35.1633H25.9619L25.9209 35.0217L19.4668 12.5168L13.0352 35.0217L12.9941 35.1633H8.25488L8.21582 35.0188L0.0664062 4.7854L0 4.53931H5.29102L5.32715 4.6897ZM95.7441 35.1633H90.8281V4.53931H95.7441V35.1633ZM87.0918 35.1077H82.1758V12.676H87.0918V35.1077ZM49.8105 35.1018C49.8206 35.0996 49.8308 35.0982 49.8408 35.0959C49.8431 35.0954 49.8453 35.0945 49.8477 35.094C49.8353 35.0968 49.8229 35.0991 49.8105 35.1018ZM50.2305 35.0002C50.2402 34.9977 50.2501 34.996 50.2598 34.9934C50.2768 34.9889 50.2936 34.9834 50.3105 34.9788C50.2839 34.986 50.2572 34.9932 50.2305 35.0002ZM82.3711 34.9124H86.8965V34.9114H82.3711V34.9124ZM51.0352 34.7542C51.0458 34.7505 51.0568 34.7481 51.0674 34.7444C51.0862 34.7378 51.1043 34.7296 51.123 34.7229C51.0938 34.7334 51.0646 34.7439 51.0352 34.7542ZM51.4062 34.6174C51.4228 34.611 51.4396 34.6054 51.4561 34.5989C51.4676 34.5943 51.4787 34.5889 51.4902 34.5842C51.4623 34.5954 51.4343 34.6064 51.4062 34.6174ZM52.165 34.2825C52.1765 34.2768 52.1887 34.2725 52.2002 34.2668C52.2104 34.2618 52.2203 34.2563 52.2305 34.2512C52.2089 34.262 52.1868 34.2718 52.165 34.2825ZM52.5322 34.093C52.5399 34.0888 52.548 34.0855 52.5557 34.0813C52.5745 34.071 52.5926 34.0595 52.6113 34.0491C52.585 34.0638 52.5587 34.0785 52.5322 34.093ZM69.75 16.5793C68.0952 16.5796 66.6896 17.3036 65.6934 18.5803C64.6943 19.8609 64.1006 21.7073 64.1006 23.9475C64.1007 26.2086 64.695 28.0535 65.6934 29.3284C66.6891 30.5995 68.0942 31.3144 69.75 31.3147C71.4719 31.3147 72.8546 30.6192 73.8115 29.3625C74.7729 28.1 75.3163 26.2555 75.3164 23.9475C75.3164 21.6609 74.7733 19.8152 73.8115 18.5471C72.8541 17.285 71.4708 16.5793 69.75 16.5793ZM108.497 16.5793C106.765 16.5794 105.367 17.2856 104.396 18.5481C103.422 19.8163 102.868 21.6615 102.868 23.9475C102.868 26.2438 103.421 28.0884 104.396 29.3538C105.366 30.6136 106.765 31.3146 108.497 31.3147C110.175 31.3146 111.568 30.6051 112.548 29.3381C113.531 28.0665 114.104 26.2211 114.104 23.9475C114.104 21.6951 113.531 19.8488 112.548 18.5715C111.568 17.299 110.174 16.5794 108.497 16.5793ZM10.8984 28.4094H10.7305V28.4104H10.8984L17.2891 4.73462H17.2881L10.8984 28.4094ZM28.0576 28.4104H28.2256L28.2246 28.4094H28.0576V28.4104ZM102.698 24.7991C102.712 25.0178 102.732 25.2319 102.756 25.4417C102.731 25.2235 102.711 25.0005 102.697 24.7727L102.698 24.7991ZM114.294 23.5667C114.298 23.6923 114.3 23.8193 114.3 23.9475L114.294 24.3694C114.298 24.2304 114.301 24.0897 114.301 23.9475C114.301 23.8193 114.298 23.6924 114.294 23.5667ZM75.5068 23.5774C75.5102 23.6995 75.5117 23.8229 75.5117 23.9475L75.5059 24.3665C75.5099 24.2284 75.5127 24.0888 75.5127 23.9475C75.5127 23.698 75.5055 23.4535 75.4922 23.2141L75.5068 23.5774ZM52.3613 21.9153L52.3457 21.6018C52.3443 21.5819 52.3424 21.5621 52.3408 21.5422C52.3504 21.6651 52.3574 21.7895 52.3613 21.9153ZM46.9561 16.3694C43.9799 16.3695 41.8558 18.5812 41.5547 21.719H52.1562C52.0683 20.1605 51.5314 18.8458 50.6631 17.9124C49.7591 16.9407 48.486 16.3695 46.9561 16.3694ZM52.167 20.4133C52.1931 20.5236 52.2153 20.6358 52.2363 20.7493C52.2152 20.6355 52.1931 20.523 52.167 20.4124V20.4133ZM65.2168 18.9153C65.2095 18.9266 65.2026 18.9381 65.1953 18.9495C65.2462 18.8701 65.2981 18.7922 65.3516 18.7161L65.2168 18.9153ZM104.831 17.7776C104.772 17.8328 104.715 17.8893 104.658 17.9475C104.715 17.8893 104.772 17.8328 104.831 17.7776ZM105.179 17.4778C105.144 17.5052 105.109 17.5334 105.075 17.5618C105.109 17.5334 105.144 17.5052 105.179 17.4778ZM105.563 17.2024C105.447 17.2774 105.334 17.358 105.224 17.4426C105.332 17.3592 105.444 17.2803 105.559 17.2063C105.56 17.2052 105.562 17.2035 105.563 17.2024ZM105.794 17.0637C105.716 17.1081 105.639 17.1539 105.563 17.2024C105.639 17.1539 105.716 17.1081 105.794 17.0637ZM106.053 16.927C105.98 16.9628 105.908 17.0001 105.838 17.0393C105.908 17.0001 105.98 16.9628 106.053 16.927ZM107.203 16.5237C106.811 16.6115 106.44 16.7399 106.093 16.9075C106.266 16.824 106.445 16.7498 106.63 16.6858C106.815 16.6215 107.007 16.5679 107.203 16.5237ZM107.841 16.4182C107.746 16.4283 107.652 16.4409 107.56 16.4553C107.652 16.4409 107.746 16.4282 107.841 16.4182Z" fill="black" />
      </mask>
      {/* text blue shadow */}
      <g mask="url(#mask0_1088_92)" className="logo-glow-animation">
        <g opacity="1" filter="url(#filter0_f_1088_92)">
          <path d="M64.1844 4.26699H57.8291L75.4283 -11.7678L97.3295 -9.42126L111.702 -2.77268L98.6983 5.63582C96.7429 5.27732 92.6168 4.77541 91.7564 5.63582C90.6809 6.71132 90.6809 9.64452 90.3876 11.8933C90.1529 13.6923 88.4647 13.5555 87.6499 13.2621L82.468 14.0443L80.9036 21.6706C80.4799 21.3773 79.3588 20.1649 78.2637 17.6619C76.8949 14.5332 75.135 13.5555 72.2995 13.2621C70.0312 13.0275 65.3251 14.6635 64.1844 15.2176V4.26699Z" fill="#0597FF" />
        </g>
        <g filter="url(#filter1_f_1088_92)">
          <path d="M64.1844 4.26699H57.8291L75.4283 -11.7678L97.3295 -9.42126L111.702 -2.77268L98.6983 5.63582C96.7429 5.27732 92.6168 4.77541 91.7564 5.63582C90.6809 6.71132 90.6809 9.64452 90.3876 11.8933C90.1529 13.6923 88.4647 13.5555 87.6499 13.2621L82.468 14.0443L80.9036 21.6706C80.4799 21.3773 79.3588 20.1649 78.2637 17.6619C76.8949 14.5332 75.135 13.5555 72.2995 13.2621C70.0312 13.0275 65.3251 14.6635 64.1844 15.2176V4.26699Z" fill="#0597FF" />
        </g>
      </g>
      {/* text dot */}
      <g className="logo-dot-animation">
        <g filter="url(#filter2_f_1088_92)">
          <ellipse cx="84.5321" cy="5.43738" rx="3.34993" ry="3.38472" transform="rotate(25.4025 84.5321 5.43738)" fill="url(#paint0_linear_1088_92)" />
        </g>
        <g filter="url(#filter3_f_1088_92)">
          <circle cx="84.7146" cy="6.02408" r="2.9332" transform="rotate(25.4025 84.7146 6.02408)" fill="url(#paint1_linear_1088_92)" />
        </g>
        <g filter="url(#filter4_f_1088_92)">
          <circle cx="84.714" cy="6.02411" r="2.9332" transform="rotate(-169.598 84.714 6.02411)" fill="url(#paint2_linear_1088_92)" />
        </g>
        <g filter="url(#filter5_f_1088_92)">
          <circle cx="84.8077" cy="6.11737" r="2.9332" transform="rotate(-169.598 84.8077 6.11737)" fill="url(#paint3_linear_1088_92)" />
        </g>
      </g>
      {/* gradients */}
      <defs>
        <filter id="filter0_f_1088_92" x="55.5021" y="-14.0948" width="58.5271" height="38.0925" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="1.1635" result="effect1_foregroundBlur_1088_92" />
        </filter>
        <filter id="filter1_f_1088_92" x="48.2473" y="-21.3496" width="73.0366" height="52.602" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="4.79089" result="effect1_foregroundBlur_1088_92" />
        </filter>
        <filter id="filter2_f_1088_92" x="80.3731" y="1.25661" width="8.31833" height="8.36178" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="0" result="effect1_foregroundBlur_1088_92" />
        </filter>
        <filter id="filter3_f_1088_92" x="79.16" y="0.469765" width="11.1088" height="11.1086" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="0" result="effect1_foregroundBlur_1088_92" />
        </filter>
        <filter id="filter4_f_1088_92" x="80.2159" y="1.52596" width="8.99593" height="8.99618" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="0.058664" result="effect1_foregroundBlur_1088_92" />
        </filter>
        <filter id="filter5_f_1088_92" x="79.371" y="0.680598" width="10.8732" height="10.8734" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="1.2515" result="effect1_foregroundBlur_1088_92" />
        </filter>
        <linearGradient id="paint0_linear_1088_92" x1="84.688" y1="6.01618" x2="87.1894" y2="8.71586" gradientUnits="userSpaceOnUse">
          <stop stopColor="#0F3DA6" />
          <stop offset="0.951923" stopColor="#3A9AFF" stopOpacity="0" />
        </linearGradient>
        <linearGradient id="paint1_linear_1088_92" x1="82.7095" y1="5.89728" x2="85.0982" y2="8.87597" gradientUnits="userSpaceOnUse">
          <stop offset="0.00961538" stopColor="#0D50E8" />
          <stop offset="0.951923" stopColor="#3A9AFF" stopOpacity="0" />
        </linearGradient>
        <linearGradient id="paint2_linear_1088_92" x1="87.705" y1="8.25537" x2="83.0602" y2="2.44777" gradientUnits="userSpaceOnUse">
          <stop stopColor="#0F3DA6" stopOpacity="0" />
          <stop offset="1" stopColor="#59ABFF" />
        </linearGradient>
        <linearGradient id="paint3_linear_1088_92" x1="87.7988" y1="8.34863" x2="85.8583" y2="3.73451" gradientUnits="userSpaceOnUse">
          <stop stopColor="#0F3DA6" stopOpacity="0" />
          <stop offset="1" stopColor="#59ABFF" />
        </linearGradient>
      </defs>
    </svg>
  );
});