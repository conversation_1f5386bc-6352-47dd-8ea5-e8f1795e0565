"use client";

import { forwardRef } from "react";
import AnimationContainer from "@/components/layout/AnimationContainer";
import InputSection from "@/components/ui/InputSection";
import ChatMessageComponent from "@/components/ui/ChatMessage";
import type { ChatMessage } from "@/types/chat";
import {
    shouldAnimate,
    shouldShowThinking,
    getAnimationCompleteHandler,
    getTasksCompleteHandler
} from "@/utils/chat";
import { useMessageAnimationState } from "@/hooks/useMessageAnimationState";

interface BobViewProps {
    messages: ChatMessage[];
    isLoading: boolean;
    isResponseReady: boolean;
    onSendMessage: (content: string) => void;
    onAnimationComplete: (messageId: string) => void;
    onAllTasksCompleted?: (messageId?: string) => void;
}

const BobView = forwardRef<HTMLDivElement, BobViewProps>(({
    messages,
    isLoading,
    isResponseReady,
    onSendMessage,
    onAnimationComplete,
    onAllTasksCompleted
}, ref) => {
    const { isAnyMessageAnimating } = useMessageAnimationState(messages, isLoading, isResponseReady);

    return (
        <AnimationContainer
            className="p-6 pt-10 h-screen max-h-screen flex flex-col justify-center items-center"
            animationType="full"
        >
            <div ref={ref} className="w-full flex-1 min-h-0 overflow-y-auto pt-5 pb-[var(--vw-1_25)] mask-fade-y flex flex-col items-center">
                <div className="w-[55%]" >
                    {messages.map((message, index) => (
                        <ChatMessageComponent
                            key={message.id}
                            message={message}
                            isAnimated={shouldAnimate(message, index, messages.length, isLoading, isResponseReady)}
                            isThinking={shouldShowThinking(message, index, messages.length, isLoading)}
                            onAnimationComplete={getAnimationCompleteHandler(message, index, messages.length, onAnimationComplete)}
                            onAllTasksCompleted={getTasksCompleteHandler(message, index, messages.length, onAllTasksCompleted)}
                            viewContext="build"
                        />
                    ))}
                </div>
            </div>
            <div className="relative w-[55%] h-fit p-2.5 glass-box">
                <InputSection
                    leftButtonsClassName="hidden"
                    rightButtonsClassName="w-full justify-between"
                    textareaMaxLines={3}
                    onSubmit={onSendMessage}
                    isLoading={isLoading || isAnyMessageAnimating}
                />
            </div>
        </AnimationContainer>
    );
});

BobView.displayName = "BobView";

export default BobView;