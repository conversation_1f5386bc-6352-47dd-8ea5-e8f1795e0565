"use client";

import { memo, useEffect, useRef } from "react";
import AnimatedText from "../AnimatedText";
import AnimationContainer from "@/components/layout/AnimationContainer";
import TasksResponse from "./TasksResponse";
import IframeMessage from "../IframeMessage";
import Image from "next/image";
import { strs } from "@/constants/pages/projects/strs";
import type { MessageType, TasksContent, IframeContent } from "@/types/chat";

interface AssistantMessageProps {
    content: string | TasksContent | IframeContent;
    type?: MessageType;
    isAnimating: boolean;
    isThinking: boolean;
    onAnimationComplete: () => void;
    taskCompletionTimes?: number[];
    onAllTasksCompleted?: () => void;
    iframeCompleted?: boolean;
    viewContext?: 'clone' | 'build';
}

const AssistantHeader = memo(({ showThinking }: { showThinking: boolean }) => (
    <AnimationContainer 
        key={showThinking ? "thinking" : "static"}
        className="mb-2 flex items-center"
        animationType="fade"
    >
        <Image 
            src="/brand/logo-dot.svg" 
            alt={strs.alt.logoDot} 
            width={50} 
            height={50} 
            className="w-[var(--text-3xl)] aspect-square" 
        />
        {showThinking ? (
            <AnimatedText 
                text={strs.chat.thinking}
                className="text-sm text-black"
                continuous={true}
            />
        ) : (
            <p className="text-sm text-black">{strs.chat.assistant}</p>
        )}
    </AnimationContainer>
));

AssistantHeader.displayName = "AssistantHeader";

const AssistantMessage = memo(({ content, type = "text", isAnimating, isThinking, onAnimationComplete, taskCompletionTimes, onAllTasksCompleted, iframeCompleted, viewContext = 'build' }: AssistantMessageProps) => {
    const hasContent = Boolean(content);
    const hasCalledComplete = useRef(false);
    
    useEffect(() => {
        if (type === "iframe" && iframeCompleted && !hasCalledComplete.current && onAnimationComplete) {
            hasCalledComplete.current = true;
            onAnimationComplete();
        }
    }, [type, iframeCompleted, onAnimationComplete]);
    
    return (
        <>
            <AssistantHeader showThinking={isThinking || isAnimating} />
            {hasContent && (
                <div key="message-content">
                    {type === "tasks" ? (
                        <TasksResponse 
                            content={content as TasksContent}
                            isAnimating={isAnimating}
                            onAnimationComplete={onAnimationComplete}
                            taskCompletionTimes={taskCompletionTimes}
                            onAllTasksCompleted={onAllTasksCompleted}
                        />
                    ) : type === "iframe" ? (
                        <IframeMessage content={content as IframeContent} isCompleted={iframeCompleted} viewContext={viewContext} />
                    ) : (
                        <AnimatedText 
                            text={content as string} 
                            className="text-sm leading-relaxed text-gray-900"
                            onComplete={isAnimating ? onAnimationComplete : undefined}
                            instant={!isAnimating}
                        />
                    )}
                </div>
            )}
        </>
    );
});

AssistantMessage.displayName = "AssistantMessage";

export default AssistantMessage;