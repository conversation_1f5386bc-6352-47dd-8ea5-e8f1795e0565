import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ProjectData } from '@/types/project';
import { getProject, saveProject, deleteProject } from '@/utils/projectStorage';

interface ProjectState {
    currentProject: ProjectData | null;
    projects: ProjectData[];
    isLoading: boolean;
    error: string | null;
    
    setCurrentProject: (project: ProjectData) => void;
    updateProject: (id: string, updates: Partial<ProjectData>) => void;
    loadProject: (id: string) => Promise<void>;
    createProject: (data: Omit<ProjectData, 'id' | 'createdAt'>) => ProjectData;
    removeProject: (id: string) => void;
    clearError: () => void;
}

export const useProjectStore = create<ProjectState>()(
    devtools(
        (set) => ({
            currentProject: null,
            projects: [],
            isLoading: false,
            error: null,
            
            setCurrentProject: (project) => set({ currentProject: project }),
            
            updateProject: (id, updates) => set((state) => ({
                projects: state.projects.map(p => 
                    p.id === id ? { ...p, ...updates } : p
                ),
                currentProject: state.currentProject?.id === id 
                    ? { ...state.currentProject, ...updates }
                    : state.currentProject
            })),
            
            loadProject: async (id) => {
                set({ isLoading: true, error: null });
                try {
                    const project = getProject(id);
                    if (project) {
                        set({ currentProject: project, isLoading: false });
                    } else {
                        set({ error: 'Project not found', isLoading: false });
                    }
                } catch {
                    set({ error: 'Failed to load project', isLoading: false });
                }
            },
            
            createProject: (data) => {
                const project: ProjectData = {
                    ...data,
                    id: Date.now().toString(),
                    createdAt: new Date().toISOString()
                };
                
                saveProject(project);
                set((state) => ({
                    projects: [...state.projects, project],
                    currentProject: project
                }));
                
                return project;
            },
            
            removeProject: (id) => {
                deleteProject(id);
                set((state) => ({
                    projects: state.projects.filter(p => p.id !== id),
                    currentProject: state.currentProject?.id === id ? null : state.currentProject
                }));
            },
            
            clearError: () => set({ error: null })
        }),
        {
            name: 'project-store'
        }
    )
);