"use client";

import AnimationContainer from "@/components/layout/AnimationContainer";
import About from "@/components/widgets/home/<USER>/About";
import Features from "@/components/widgets/home/<USER>/Features";
import Hero from "@/components/widgets/home/<USER>/Hero";
import { useAuth } from "@clerk/nextjs";

const Page = () => {
  const { isSignedIn } = useAuth();

  return (
    <div className="relative w-full h-fit">
      <AnimationContainer
        className="relative z-10 w-full h-fit flex flex-col gap-6"
        animationType="full"
      >
        <Hero />
        {!isSignedIn && (
          <>
            <About />
            <Features />
          </>
        )}
      </AnimationContainer>
    </div>
  );
};

export default Page;
