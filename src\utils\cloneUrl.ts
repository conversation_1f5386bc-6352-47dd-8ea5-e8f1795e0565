export const extractUrlFromClonePrompt = (prompt: string): string | null => {
    const cleanPrompt = prompt.replace(/^Clone this website URL:\s*/i, '').trim();
    
    const urlPattern = /(https?:\/\/[^\s]+)/i;
    const match = cleanPrompt.match(urlPattern);
    
    if (match && isValidUrl(match[1])) {
        return match[1];
    }
    
    if (cleanPrompt && !cleanPrompt.includes('://')) {
        const withProtocol = `https://${cleanPrompt}`;
        if (isValidUrl(withProtocol)) {
            return withProtocol;
        }
    }
    
    return null;
};

export const isValidUrl = (url: string): boolean => {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
};