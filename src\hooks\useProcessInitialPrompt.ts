import { useEffect, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export const useProcessInitialPrompt = (
    initialPrompt: string,
    handleSendMessage: (content: string, callback?: () => void) => void,
    scrollCallback?: () => void
) => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const hasProcessedInitialPrompt = useRef(false);

    useEffect(() => {
        if (initialPrompt && !hasProcessedInitialPrompt.current) {
            hasProcessedInitialPrompt.current = true;
            handleSendMessage(initialPrompt, scrollCallback);
            
            const mode = searchParams.get('mode');
            const newUrl = mode ? `/projects?mode=${mode}` : '/projects';
            router.replace(newUrl, { scroll: false });
        }
    }, [initialPrompt, router, handleSendMessage, scrollCallback, searchParams]);
};