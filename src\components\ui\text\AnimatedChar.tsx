"use client";

import { useState, useEffect, memo } from "react";
import { AnimatedCharProps } from "@/types/components/AnimatedText";

const AnimatedChar = memo(({ 
    char, 
    duration, 
    colorTransition, 
    colorDelay, 
    startColor, 
    delay 
}: AnimatedCharProps) => {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        const timer = setTimeout(() => setIsVisible(true), delay);
        return () => clearTimeout(timer);
    }, [delay]);

    const transitionStyle = delay === 0 ? 'none' : colorTransition
        ? `opacity ${duration}ms, color ${duration}ms ${colorDelay}ms`
        : `opacity ${duration}ms`;

    const colorStyle = colorTransition && !isVisible ? startColor : undefined;

    return (
        <span
            className={`inline ${delay > 0 ? `transition-opacity ${colorTransition ? "transition-colors" : ""}` : ""} ${
                isVisible ? "opacity-100" : delay === 0 ? "opacity-100" : "opacity-0"
            }`}
            style={{
                transition: transitionStyle,
                color: colorStyle,
            }}
        >
            {char}
        </span>
    );
});

AnimatedChar.displayName = "AnimatedChar";

export default AnimatedChar;