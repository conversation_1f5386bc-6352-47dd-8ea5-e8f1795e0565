import { memo } from "react";
import LogoAnimation from "@/components/ui/LogoAnimation";

interface IframeLoadingOverlayProps {
    isLoading: boolean;
}

const IframeLoadingOverlay = memo(({ isLoading }: IframeLoadingOverlayProps) => {
    if (!isLoading) return null;

    return (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-white rounded">
            <LogoAnimation className="h-10 w-auto" />
        </div>
    );
});

IframeLoadingOverlay.displayName = "IframeLoadingOverlay";

export default IframeLoadingOverlay;