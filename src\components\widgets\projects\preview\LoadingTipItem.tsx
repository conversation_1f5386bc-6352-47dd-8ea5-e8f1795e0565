"use client";

import { memo } from "react";
import Image from "next/image";

export interface LoadingTip {
    icon: string;
    text: string;
}

const LoadingTipItem = memo(({ icon, text }: LoadingTip) => (
    <div className="flex items-center gap-2">
        <Image
            src={icon}
            alt=""
            width={50}
            height={50}
            className="h-[var(--text-sm)] w-auto object-contain"
        />
        <h3 className="text-sm">{text}</h3>
    </div>
));

LoadingTipItem.displayName = "LoadingTipItem";

export default LoadingTipItem;