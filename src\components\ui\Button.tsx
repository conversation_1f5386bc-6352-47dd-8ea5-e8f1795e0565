"use client";

import { memo } from "react";
import { ButtonProps } from "@/types/components/Button";
import { useButtonEffects } from "@/hooks/useButtonEffects";

export default memo(function Button({ 
  children, 
  className = '', 
  styleClassName = 'white-button',
  onClick 
}: ButtonProps) {
  const { handleMouseEnter, handleClick, buttonClassName } = useButtonEffects();

  return (
    <button 
      className={`button text-xs ${styleClassName} ${className} ${buttonClassName}`}
      onClick={(e) => handleClick(e, onClick)}
      onMouseEnter={handleMouseEnter}
    >
      {children}
    </button>
  )
});