"use client"

import InputSection from "@/components/ui/InputSection"
import ShowreelButton from "@/components/ui/ShowreelButton"
import { strs } from "@/constants/pages/home/<USER>/strs"

export default function Hero() {
  return (
    <div className="relative h-screen flex flex-col p-6 gap-[1.25rem] items-center justify-center">
      <div className="relative z-10 flex flex-col text-center items-center gap-2 text-black">
        <h1 className="text-5xl font-medium gradient-text">{strs.title}</h1>
        <h2 className="text-xl">{strs.subtitle}</h2>
      </div>
      <div className="relative z-0 w-full h-fit p-5 flex items-center justify-center">
        <div className="relative z-10 w-[50%] h-fit p-5 glass-box">
          <InputSection />
        </div>
        <video src="/videos/waves.mp4" className="absolute z-0 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-auto mask-fade-edges-2px" autoPlay loop muted />
      </div>
      <ShowreelButton className="!absolute bottom-10" />
    </div>
  )
}