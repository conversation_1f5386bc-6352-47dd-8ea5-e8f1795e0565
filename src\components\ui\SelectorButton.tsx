"use client";

import React, { useRef, useEffect, memo } from "react";
import { SelectorButtonProps } from "@/types/components/SelectorButton";

const SelectorButton = memo<SelectorButtonProps>(({
    options,
    activeValue,
    onValueChange,
    className = "",
    buttonClassName = "",
    wrapperClassName = "",
}) => {
    const hoverRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const container = containerRef.current;
        const hoverElement = hoverRef.current;

        if (!container || !hoverElement) return;

        const moveHoverBlock = (target: HTMLElement) => {
            if (!target) return;
            const targetRect = target.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();

            hoverElement.style.width = `${targetRect.width}px`;
            hoverElement.style.transform = `translateX(${targetRect.left - containerRect.left}px)`;
        };

        const updatePosition = () => {
            const activeButton = container.querySelector(
                `[data-value="${activeValue}"]`,
            ) as HTMLElement;
            if (activeButton) moveHoverBlock(activeButton);
        };

        updatePosition();

        const resizeObserver = new ResizeObserver(updatePosition);
        resizeObserver.observe(container);

        return () => {
            resizeObserver.disconnect();
        };
    }, [activeValue]);

    return (
        <div className={`relative w-full p-1 white-box rounded-full ${wrapperClassName}`} >
            <div
                ref={containerRef}
                className={`relative overflow-hidden cursor-pointer w-full px-0 rounded-full ${className}`}
            >
                {options.map((option) => (
                    <button
                        key={option.value}
                        data-value={option.value}
                        className={`relative h-10 w-1/2 px-6 rounded text-nowrap transition-all duration-300 ease-in-out cursor-pointer z-[1] ${activeValue === option.value ? "" : "bg-transparent"
                            } ${buttonClassName}`}
                        onClick={() => onValueChange(option.value)}
                    >
                        {typeof option.label === 'string' ? (
                            <p className="text-sm text-black transition-colors duration-300 ease-in-out cursor-pointer">
                                {option.label}
                            </p>
                        ) : (
                            <div className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${activeValue === option.value ? "opacity-100" : "opacity-50"}`}>
                                {option.label}
                            </div>
                        )}
                    </button>
                ))}
                <div
                    ref={hoverRef}
                    className="absolute top-0 left-0 h-full rounded-full overflow-hidden opacity-100 pointer-events-none z-0 transition-all duration-400 ease-out"
                >
                    <div className="white-button w-full h-full rounded-full" />
                </div>
            </div>
        </div>
    );
});

SelectorButton.displayName = "SelectorButton";

export default SelectorButton;
