"use client";

import { memo } from "react";
import Button from "./Button";
import Image from "next/image";

interface IconButtonProps {
    icon: string;
    alt: string;
    label: string;
    onClick?: () => void;
    className?: string;
}

const IconButton = memo(({ icon, alt, label, onClick, className = "" }: IconButtonProps) => {
    return (
        <Button className={`flex items-center pl-[0.4rem] gap-2 ${className}`} onClick={onClick}>
            <div className="button-circle white-circle">
                <Image 
                    src={icon}
                    alt={alt}
                    width={50} 
                    height={50} 
                    className="w-[40%] h-[40%] object-contain" 
                />
            </div>
            {label}
        </Button>
    );
});

IconButton.displayName = "IconButton";

export default IconButton;