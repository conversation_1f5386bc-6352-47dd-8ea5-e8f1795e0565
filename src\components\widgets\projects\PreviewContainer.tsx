"use client";

import { memo } from "react";
import { useDevicePreview } from "@/hooks/useDevicePreview";
import PreviewToolbar from "./preview/PreviewToolbar";
import PreviewFrame from "./preview/PreviewFrame";
interface PreviewContainerProps {
    showPreview: boolean;
    previewStage: number;
    clonedUrl?: string;
}

const PreviewContainer = memo(({ showPreview, previewStage, clonedUrl }: PreviewContainerProps) => {
    const { activeDevice, setActiveDevice, deviceClasses } = useDevicePreview();

    return (
        <div className="col-span-7 h-full white-box rounded p-2.5">
            <div className="w-full h-full p-2.5 gap-5 flex flex-col rounded big-box-gradient">
                <PreviewToolbar 
                    activeDevice={activeDevice}
                    onDeviceChange={setActiveDevice}
                />
                <PreviewFrame 
                    deviceClasses={deviceClasses}
                    activeDevice={activeDevice}
                    showLoadingState={!showPreview}
                    previewStage={previewStage}
                    clonedUrl={clonedUrl}
                />
            </div>
        </div>
    );
});

PreviewContainer.displayName = "PreviewContainer";

export default PreviewContainer;