import { 
    Layout, 
    Package, 
    Type
} from "lucide-react";
import type { BrowseCategory } from "@/types/browse";

export const browseCategories: BrowseCategory[] = [
    {
        id: "sections",
        label: "Sections",
        icon: <Layout className="w-full h-full" />,
        subItems: [
            { id: "hero", label: "Hero", count: 15 },
            { id: "features", label: "Features", count: 12 },
            { id: "pricing", label: "Pricing", count: 8 },
            { id: "testimonials", label: "Testimonials", count: 10 },
            { id: "contact", label: "Contact", count: 6 },
            { id: "footer", label: "Footer", count: 9 },
            { id: "about", label: "About", count: 7 },
            { id: "team", label: "Team", count: 5 },
            { id: "faq", label: "FAQ", count: 4 },
            { id: "cta", label: "Call to Action", count: 8 },
            { id: "stats", label: "Stats", count: 6 },
            { id: "portfolio", label: "Portfolio", count: 11 },
        ]
    },
    {
        id: "components",
        label: "Components",
        icon: <Package className="w-full h-full" />,
        subItems: [
            { id: "buttons", label: "Buttons", count: 18 },
            { id: "cards", label: "Cards", count: 14 },
            { id: "forms", label: "Forms", count: 10 },
            { id: "navigation", label: "Navigation", count: 12 },
            { id: "modals", label: "Modals", count: 7 },
            { id: "badges", label: "Badges", count: 5 },
            { id: "tabs", label: "Tabs", count: 8 },
            { id: "accordions", label: "Accordions", count: 6 },
            { id: "alerts", label: "Alerts", count: 9 },
            { id: "tooltips", label: "Tooltips", count: 4 },
            { id: "progress", label: "Progress Bars", count: 5 },
            { id: "breadcrumbs", label: "Breadcrumbs", count: 3 },
        ]
    },
    {
        id: "text",
        label: "Text",
        icon: <Type className="w-full h-full" />,
        subItems: [
            { id: "headings", label: "Headings", count: 8 },
            { id: "paragraphs", label: "Paragraphs", count: 6 },
            { id: "quotes", label: "Quotes", count: 4 },
            { id: "lists", label: "Lists", count: 5 },
            { id: "typography", label: "Typography", count: 9 },
            { id: "labels", label: "Labels", count: 3 },
            { id: "captions", label: "Captions", count: 4 },
            { id: "highlights", label: "Text Highlights", count: 7 },
            { id: "dividers", label: "Dividers", count: 5 },
            { id: "code", label: "Code Blocks", count: 6 },
        ]
    }
];