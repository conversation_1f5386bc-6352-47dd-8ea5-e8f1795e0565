import type { ChatMessage } from "@/types/chat";

export const isLastAssistantMessage = (message: ChatMessage, index: number, totalMessages: number) => {
    return message.role === "assistant" && index === totalMessages - 1;
};

export const shouldAnimate = (message: ChatMessage, index: number, totalMessages: number, isLoading: boolean, isResponseReady: boolean) => {
    return isLastAssistantMessage(message, index, totalMessages) && isResponseReady && !message.animationComplete;
};

export const shouldShowThinking = (message: ChatMessage, index: number, totalMessages: number, isLoading: boolean) => {
    return isLastAssistantMessage(message, index, totalMessages) && isLoading;
};

export const getAnimationCompleteHandler = (
    message: ChatMessage, 
    index: number, 
    totalMessages: number, 
    onAnimationComplete: (messageId: string) => void
) => {
    return isLastAssistantMessage(message, index, totalMessages) 
        ? () => onAnimationComplete(message.id) 
        : undefined;
};

export const getTasksCompleteHandler = (
    message: ChatMessage,
    index: number,
    totalMessages: number,
    onAllTasksCompleted?: (messageId?: string) => void
) => {
    const isTaskMessage = message.type === "tasks";
    return isTaskMessage && isLastAssistantMessage(message, index, totalMessages)
        ? () => onAllTasksCompleted?.(message.id)
        : undefined;
};