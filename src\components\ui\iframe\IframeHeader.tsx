"use client";

import { memo } from "react";
import Image from "next/image";
import AnimationContainer from "@/components/layout/AnimationContainer";

interface IframeHeaderProps {
    isCompleted: boolean;
}

const IframeHeader = memo(({ isCompleted }: IframeHeaderProps) => {
    return (
        <div className="flex items-center">
            <Image
                src="/brand/logo-dot.svg"
                alt=""
                width={50}
                height={50}
                className="w-[var(--text-3xl)] aspect-square"
            />
            <AnimationContainer
                key={isCompleted ? 'completed' : 'loading'}
                className="text-sm"
                animationType="fade"
            >
                <p className="text-sm">{isCompleted ? 'Website cloned successfully' : 'Cloning website vita-lenta.com'}</p>
            </AnimationContainer>
        </div>
    );
});

IframeHeader.displayName = "IframeHeader";

export default IframeHeader;