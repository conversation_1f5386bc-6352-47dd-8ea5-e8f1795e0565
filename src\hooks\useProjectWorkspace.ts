import { useRef, useCallback, use, useEffect } from "react";
import { useUIStore } from "@/stores/uiStore";
import { useProjectStore } from "@/stores/projectStore";
import { useChatStore } from "@/stores/chatStore";
import { ChatContainerRef } from "@/components/widgets/projects/ChatContainer";
import { scrollToBottom as scrollUtil } from "@/utils/scroll";
import { getAiResponses } from "@/utils/aiResponses";

interface UseProjectWorkspaceParams {
    params: Promise<{ id: string }>;
}

export const useProjectWorkspace = ({ params }: UseProjectWorkspaceParams) => {
    const resolvedParams = use(params);
    
    // Direct store access with selectors
    const projectData = useProjectStore(state => state.currentProject);
    const loadProject = useProjectStore(state => state.loadProject);
    
    const messages = useChatStore(state => state.messages);
    const isLoading = useChatStore(state => state.isLoading);
    const isResponseReady = useChatStore(state => state.isResponseReady);
    const sendMessage = useChatStore(state => state.sendMessage);
    const handleAnimationComplete = useChatStore(state => state.handleAnimationComplete);
    
    const activeView = useUIStore(state => state.activeView);
    const setActiveView = useUIStore(state => state.setActiveView);
    const allTasksCompleted = useUIStore(state => state.allTasksCompleted);
    const previewStage = useUIStore(state => state.previewStage);
    const handleAllTasksCompleted = useUIStore(state => state.handleAllTasksCompleted);
    const handleResetPreview = useUIStore(state => state.handleResetPreview);
    const hasTransitioned = useUIStore(state => state.hasTransitioned);
    const setHasTransitioned = useUIStore(state => state.setHasTransitioned);
    const bobResponseCount = useUIStore(state => state.bobResponseCount);
    const incrementBobResponseCount = useUIStore(state => state.incrementBobResponseCount);
    const resetBobResponseCount = useUIStore(state => state.resetBobResponseCount);
    
    const chatContainerRef = useRef<ChatContainerRef>(null);
    const cloningViewRef = useRef<HTMLDivElement>(null);
    const hasProcessedPromptRef = useRef(false);
    const processedProjectIdRef = useRef<string | null>(null);
    
    // Load project on mount
    useEffect(() => {
        loadProject(resolvedParams.id);
    }, [resolvedParams.id, loadProject]);
    
    // Computed values
    const isCloneMode = projectData?.mode === 'clone';
    const isBobMode = projectData?.mode === 'bob';
    const iframeMessage = messages.find(
        msg => msg.type === 'iframe' && msg.role === 'assistant'
    );
    const isCloningComplete = iframeMessage?.iframeCompleted && !iframeMessage?.manuallyStopped || false;
    const clonedUrl = isCloneMode && iframeMessage 
        ? (iframeMessage.content as { url?: string })?.url 
        : undefined;
    const shouldShowCloneView = isCloneMode && !hasTransitioned && !isCloningComplete;
    const shouldShowBobView = isBobMode && !hasTransitioned && bobResponseCount < 1;
    
    // Handle clone view transition
    useEffect(() => {
        if (isCloneMode && isCloningComplete && !hasTransitioned) {
            const timer = setTimeout(() => {
                setHasTransitioned(true);
            }, 1000);
            return () => clearTimeout(timer);
        }
    }, [isCloneMode, isCloningComplete, hasTransitioned, setHasTransitioned]);
    
    // Handle bob mode transition
    useEffect(() => {
        if (isBobMode && bobResponseCount >= 1 && !hasTransitioned) {
            const timer = setTimeout(() => {
                setHasTransitioned(true);
            }, 500);
            return () => clearTimeout(timer);
        }
    }, [isBobMode, bobResponseCount, hasTransitioned, setHasTransitioned]);
    
    // Reset bob response count on mount
    useEffect(() => {
        if (isBobMode) {
            resetBobResponseCount();
        }
    }, [isBobMode, resetBobResponseCount]);
    
    // Scroll handling
    const scrollToBottom = useCallback(() => {
        const ref = shouldShowCloneView || shouldShowBobView ? cloningViewRef : chatContainerRef;
        scrollUtil(ref, shouldShowCloneView || shouldShowBobView);
    }, [shouldShowCloneView, shouldShowBobView]);
    
    // Message submission
    const handleMessageSubmit = useCallback((content: string, shouldScroll: boolean = true) => {
        const responses = getAiResponses(projectData) || [];
        sendMessage(content, responses, isCloneMode, handleResetPreview).then(() => {
            if (shouldScroll) {
                scrollToBottom();
            }
        });
    }, [projectData, isCloneMode, sendMessage, handleResetPreview, scrollToBottom]);
    
    // Process initial prompt
    useEffect(() => {
        if (projectData?.prompt && projectData.id !== processedProjectIdRef.current) {
            if (!hasProcessedPromptRef.current) {
                hasProcessedPromptRef.current = true;
                processedProjectIdRef.current = projectData.id;
                // Don't scroll for initial prompt
                handleMessageSubmit(projectData.prompt, false);
            }
        }
    }, [projectData, handleMessageSubmit]);
    
    return {
        // Data
        projectData,
        messages,
        isLoading,
        isResponseReady,
        
        // UI State
        activeView,
        setActiveView,
        allTasksCompleted,
        previewStage,
        shouldShowCloneView,
        shouldShowBobView,
        clonedUrl,
        
        // Refs
        chatContainerRef,
        cloningViewRef,
        
        // Actions
        handleMessageSubmit,
        handleAnimationComplete,
        handleAllTasksCompleted,
        incrementBobResponseCount
    };
};