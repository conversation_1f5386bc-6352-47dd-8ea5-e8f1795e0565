"use client";

import { memo, useState, useCallback } from "react";
import type { ChatMessage } from "@/types/chat";
import { ChatMessageProps } from "@/types/components/ChatMessage";
import UserMessage from "./chat/UserMessage";
import AssistantMessage from "./chat/AssistantMessage";

const ChatMessage = memo(({ message, isAnimated = false, isThinking = false, onAnimationComplete, onAllTasksCompleted, viewContext = 'build' }: ChatMessageProps) => {
    const isUser = message.role === "user";
    const [hasCompletedAnimation, setHasCompletedAnimation] = useState(false);
    const isStillAnimating = isAnimated && !hasCompletedAnimation;

    const handleAnimationComplete = useCallback(() => {
        setHasCompletedAnimation(true);
        onAnimationComplete?.();
    }, [onAnimationComplete]);

    const isTasksResponse = message.type === "tasks" && message.role === "assistant";
    const isIframeResponse = message.type === "iframe" && message.role === "assistant";
    const isFullWidthResponse = isTasksResponse || isIframeResponse;
    const messageClasses = `w-full flex ${isUser ? "justify-end" : "justify-start"} mb-6`;
    const containerClasses = `${isFullWidthResponse ? "w-full" : "max-w-[85%]"} ${isUser ? "order-2" : "order-1"}`;

    return (
        <div className={messageClasses}>
            <div className={containerClasses}>
                {isUser ? (
                    <UserMessage content={message.content as string} />
                ) : (
                    <AssistantMessage 
                        content={message.content}
                        type={message.type}
                        isAnimating={isStillAnimating}
                        isThinking={isThinking}
                        onAnimationComplete={handleAnimationComplete}
                        taskCompletionTimes={message.taskCompletionTimes}
                        onAllTasksCompleted={onAllTasksCompleted}
                        iframeCompleted={message.iframeCompleted}
                        viewContext={viewContext}
                    />
                )}
            </div>
        </div>
    );
});

ChatMessage.displayName = "ChatMessage";

export default ChatMessage;