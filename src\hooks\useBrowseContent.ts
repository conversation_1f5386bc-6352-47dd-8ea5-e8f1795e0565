import { useMemo } from "react";
import { browseContentData } from "@/data/browseContentData";

export const useBrowseContent = (category: string | null, subItem: string | null) => {
    const sections = useMemo(() => {
        if (!category) return [];
        
        const categoryData = browseContentData[category];
        if (!categoryData) return [];
        
        if (subItem && categoryData[subItem]) {
            return [{
                title: subItem,
                items: categoryData[subItem]
            }];
        }
        
        return Object.entries(categoryData).map(([key, items]) => ({
            title: key,
            items
        }));
    }, [category, subItem]);

    const contentKey = useMemo(() => {
        return subItem || category || 'empty';
    }, [category, subItem]);

    return { sections, contentKey };
};